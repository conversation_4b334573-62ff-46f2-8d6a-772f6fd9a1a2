import Foundation
import CoreGraphics

/// Менеджер состояний активности для унифицированной системы
/// Управляет переходами между состояниями и логикой отдыха
class ActivityStateManager {
    
    // MARK: - Types
    
    /// Состояния системы активности
    enum ActivityState {
        case working           // Пользователь работает
        case awayShort        // Отошел на короткое время (0-2 мин)
        case awayMedium       // Отошел на среднее время (2-10 мин)
        case awayLong         // Отошел на долгое время (10-17 мин)
        case awayVeryLong     // Отошел очень надолго (17+ мин)
        case formalRest       // Формальный отдых (пользователь нажал "Отдых")
    }
    
    /// Тип сообщения при возвращении
    enum ReturnMessage {
        case resumeSilently           // 0-2 мин: продолжить молча
        case partialRest             // 2-10 мин: частичный отдых
        case chooseRestOrWork        // 10-17 мин: выбор между отдыхом и работой
        case fullRest                // 17+ мин: полноценный отдых
    }
    
    // MARK: - Configuration
    
    /// Пороги времени в секундах
    private let shortAwayThreshold: TimeInterval = 1 * 60      // 1 минута (для тестирования)
    private let mediumAwayThreshold: TimeInterval = 10 * 60    // 10 минут
    private let longAwayThreshold: TimeInterval = 17 * 60      // 17 минут

    /// НОВАЯ ЛОГИКА: Разумные пороги для интервалов
    private let pauseIntervalThreshold: TimeInterval = 10 * 60  // 10 минут - приостановка таймера
    private let resetIntervalThreshold: TimeInterval = 15 * 60  // 15 минут - сброс интервала
    
    // MARK: - State
    
    /// Текущее состояние
    private(set) var currentState: ActivityState = .working
    
    /// Время начала текущего состояния
    private var stateStartTime = Date()
    
    /// Время последней активности
    private var lastActivityTime = Date()

    /// Время начала неактивности (для точного расчета времени отсутствия)
    private var inactivityStartTime: Date?

    /// Активен ли менеджер
    public var isActive = false
    
    /// Остановлены ли счетчики из-за неактивности (УСТАРЕЛО - заменено на новую логику)
    private var countersStoppedDueToInactivity = false

    /// НОВАЯ ЛОГИКА: Флаги для отслеживания состояния при неактивности
    private var intervalPausedDueToInactivity = false
    private var intervalResetDueToInactivity = false
    
    // MARK: - Dependencies

    /// Трекер активности по минутам
    let minuteTracker = MinuteActivityTracker()

    /// Ссылка на PomodoroTimer для проверки формальных состояний
    weak var pomodoroTimer: PomodoroTimer?
    
    // MARK: - Callbacks
    
    /// Вызывается при изменении состояния
    var onStateChanged: ((ActivityState, ActivityState) -> Void)?
    
    /// Вызывается когда нужно остановить счетчики
    var onStopCounters: (() -> Void)?
    
    /// Вызывается когда нужно возобновить счетчики
    var onResumeCounters: (() -> Void)?

    /// Вызывается когда интервал сброшен из-за длительной неактивности
    var onIntervalReset: (() -> Void)?
    
    /// Вызывается когда пользователь возвращается после отсутствия
    var onUserReturned: ((ReturnMessage, TimeInterval) -> Void)?

    /// Вызывается при длительной неактивности для закрытия окон
    var onLongInactivity: ((TimeInterval) -> Void)?

    /// Вызывается когда нужно показать предложение неформального отдыха
    var onInformalRestSuggestion: (() -> Void)?

    /// Вызывается когда нужно показать выбор выходного дня
    var onWeekendChoice: (() -> Void)?

    /// Вызывается когда завершена неформальная сессия (для EarlyEngagementSystem)
    var onInformalSessionCompleted: ((TimeInterval, UUID?) -> Void)?

    /// Вызывается когда нужно сбросить интервал из-за длительной неактивности (15+ минут)
    var onResetIntervalDueToInactivity: ((TimeInterval) -> Void)?

    // MARK: - Informal Session Detection

    /// История активности по минутам для определения неформальных сессий
    private var minuteActivityHistory: [Bool] = []

    /// Максимальное количество минут в истории (52 минуты)
    private let maxHistoryMinutes = 52

    /// Минимальное количество активных минут для срабатывания (42 из 52)
    private let minActiveMinutesForSuggestion = 42

    /// Время последнего предложения отдыха (для cooldown)
    private var lastRestSuggestionTime: Date?

    /// Cooldown между предложениями отдыха (10 минут)
    private let restSuggestionCooldown: TimeInterval = 10 * 60

    // MARK: - Return Stabilization

    /// Время начала стабилизации возвращения
    private var returnStabilizationStartTime: Date?

    /// Требуемое время стабилизации для показа окон возвращения (3 секунды = 1 сегмент)
    private let returnStabilizationDuration: TimeInterval = 3.0

    /// Время отсутствия для показа окна возвращения (сохраняется до стабилизации)
    private var pendingReturnAwayTime: TimeInterval?

    /// Таймер для проверки стабилизации возвращения
    private var stabilizationTimer: Timer?

    /// Таймер для быстрого обнаружения возвращения пользователя (работает только при неактивности)
    private var quickReturnDetectionTimer: Timer?

    // MARK: - Public Methods
    
    /// Запускает менеджер состояний
    func start() {
        logInfo("ActivityState", "🎯 ОТЛАДКА - Начинаем start()")

        guard !isActive else {
            logInfo("ActivityState", "🎯 ОТЛАДКА - Уже активен, выходим")
            return
        }

        logInfo("ActivityState", "🎯 ОТЛАДКА - Устанавливаем состояние")
        isActive = true
        currentState = .working
        stateStartTime = Date()
        lastActivityTime = Date()
        countersStoppedDueToInactivity = false

        // ИСПРАВЛЕНИЕ БАГА: Проверяем нужно ли сбросить историю при запуске
        // Сбрасываем только если история слишком старая или содержит аномальные данные
        checkAndResetHistoryOnStartup()

        logInfo("ActivityState", "🎯 ОТЛАДКА - Запускаем трекер активности")
        // Запускаем трекер активности
        minuteTracker.startTracking()
        logInfo("ActivityState", "🎯 ОТЛАДКА - Трекер запущен")

        logInfo("ActivityState", "🎯 ОТЛАДКА - Устанавливаем колбэк")
        minuteTracker.onMinuteCompleted = { [weak self] isActive in
            logInfo("ActivityState", "🎯 ОТЛАДКА - Получен колбэк от трекера: \(isActive)")
            self?.handleMinuteActivity(isActive)

            // Записываем активность для определения неформальных сессий
            self?.recordMinuteActivity(isActive: isActive)
        }
        logInfo("ActivityState", "🎯 ОТЛАДКА - Колбэк установлен")

        logInfo("ActivityState", "🎯 Запущен в состоянии \(currentState)")
    }
    
    /// Останавливает менеджер
    func stop() {
        guard isActive else { return }
        
        isActive = false
        minuteTracker.stopTracking()
        
        logInfo("ActivityState", "🎯 Остановлен")
    }
    
    /// Уведомляет о начале формального отдыха
    func startFormalRest() {
        let previousState = currentState
        currentState = .formalRest
        stateStartTime = Date()
        
        logInfo("ActivityState", "🎯 Переход в формальный отдых")
        onStateChanged?(previousState, currentState)
    }
    
    /// Уведомляет об окончании формального отдыха
    func endFormalRest() {
        let previousState = currentState
        currentState = .working
        stateStartTime = Date()
        lastActivityTime = Date()
        countersStoppedDueToInactivity = false
        
        // Возобновляем счетчики если они были остановлены
        onResumeCounters?()
        
        logInfo("ActivityState", "🎯 Возврат к работе после формального отдыха")
        onStateChanged?(previousState, currentState)
    }
    
    /// Принудительно сбрасывает состояние после реального сна компьютера
    func resetAfterSleep() {
        let previousState = currentState
        currentState = .working
        stateStartTime = Date()
        lastActivityTime = Date()
        countersStoppedDueToInactivity = false

        // Возобновляем счетчики
        onResumeCounters?()

        logInfo("ActivityState", "🎯 ActivityStateManager: Сброс после реального сна компьютера")
        onStateChanged?(previousState, currentState)
    }

    /// Обрабатывает сон с учетом длительности (правильная логика из тестов)
    func resetAfterSleep(sleepDuration: TimeInterval) {
        let sleepMinutes = Int(sleepDuration / 60)
        let resetThreshold: TimeInterval = 15 * 60 // 15 минут

        if sleepDuration > 0 && sleepDuration < resetThreshold {
            // Короткий сон - записываем как неактивные минуты
            logInfo("ActivityState", "🎯 Короткий сон (\(sleepMinutes) мин) - записываем неактивность")
            for _ in 0..<sleepMinutes {
                recordMinuteActivity(isActive: false)
            }
        } else if sleepDuration >= resetThreshold {
            // Длительный сон - сбрасываем лог
            logInfo("ActivityState", "🎯 Длительный сон (\(sleepMinutes) мин) - сбрасываем историю активности")
            minuteActivityHistory.removeAll()
            lastRestSuggestionTime = nil
        }

        // Вызываем обычный сброс состояния
        resetAfterSleep()

        logInfo("ActivityState", "🎯 ActivityStateManager: Обработка сна (\(sleepMinutes) мин) завершена")
    }

    /// Проверяет и сбрасывает историю активности при запуске если нужно
    private func checkAndResetHistoryOnStartup() {
        // Проверяем есть ли аномальные данные (слишком много активных минут)
        let activeMinutes = minuteActivityHistory.filter { $0 }.count
        let totalMinutes = minuteActivityHistory.count

        // Если история содержит критическое количество активных минут (≥42 из 52)
        // это может привести к немедленному запуску неформальной сессии
        if activeMinutes >= minActiveMinutesForSuggestion && totalMinutes >= maxHistoryMinutes {
            logInfo("ActivityState", "🔄 КРИТИЧЕСКАЯ ИСТОРИЯ: \(activeMinutes)/\(totalMinutes) активных минут - сбрасываем для предотвращения автозапуска")
            minuteActivityHistory.removeAll()
            lastRestSuggestionTime = nil
        } else if totalMinutes > 0 {
            logInfo("ActivityState", "🔄 Сохраняем историю активности: \(activeMinutes)/\(totalMinutes) активных минут")
        } else {
            logInfo("ActivityState", "🔄 История активности пуста - начинаем с чистого листа")
        }
    }

    /// Записывает неактивные минуты за время сна/неактивности
    /// - Parameter sleepDurationMinutes: длительность сна в минутах
    func recordSleepInactivity(sleepDurationMinutes: Int) {
        logInfo("ActivityState", "🌙 ActivityStateManager: Записываем \(sleepDurationMinutes) неактивных минут за время сна")

        // Записываем неактивные минуты за время сна
        for _ in 0..<sleepDurationMinutes {
            minuteActivityHistory.append(false)
        }

        // Ограничиваем размер истории
        while minuteActivityHistory.count > maxHistoryMinutes {
            minuteActivityHistory.removeFirst()
        }

        let activeCount = minuteActivityHistory.filter { $0 }.count
        logInfo("ActivityState", "🌙 После записи сна: история \(minuteActivityHistory.count) мин, активных: \(activeCount)")

        // Проверяем условия для предложения отдыха
        checkForInformalRestSuggestion()
    }

    /// Обрабатывает возвращение после длительной неактивности (НЕ сна)
    func handleReturnAfterInactivity(duration: TimeInterval) {
        logInfo("ActivityState", "🎯 Возвращение после \(Int(duration/60)) мин неактивности")

        // Обновляем время последней активности
        lastActivityTime = Date()

        // Если счетчики были остановлены, возобновляем их
        if countersStoppedDueToInactivity {
            countersStoppedDueToInactivity = false
            onResumeCounters?()
            logInfo("ActivityState", "🎯 Счетчики возобновлены после возвращения")
        }

        // Переходим в состояние работы
        let previousState = currentState
        currentState = .working
        stateStartTime = Date()

        onStateChanged?(previousState, currentState)
    }

    /// Принудительно сбрасывает состояние (например, после долгого сна) - УСТАРЕВШИЙ МЕТОД
    @available(*, deprecated, message: "Используйте resetAfterSleep() или handleReturnAfterInactivity()")
    func resetAfterLongSleep() {
        resetAfterSleep()
    }
    
    /// Возвращает текущее состояние и время в нем
    func getCurrentStateInfo() -> (state: ActivityState, timeInState: TimeInterval) {
        let timeInState = Date().timeIntervalSince(stateStartTime)
        return (currentState, timeInState)
    }
    
    /// Возвращает время последней активности
    func getTimeSinceLastActivity() -> TimeInterval {
        return Date().timeIntervalSince(lastActivityTime)
    }
    
    /// Проверяет, остановлены ли счетчики
    func areCountersStopped() -> Bool {
        return countersStoppedDueToInactivity
    }
    
    // MARK: - Private Methods
    
    /// Обрабатывает результат активности за минуту
    private func handleMinuteActivity(_ isActive: Bool) {
        logInfo("ActivityState", "🎯 Минута активности: \(isActive ? "ДА" : "НЕТ")")
        
        if isActive {
            handleUserActivity()
        } else {
            handleUserInactivity()
        }
    }
    
    /// Обрабатывает активность пользователя
    private func handleUserActivity() {
        logInfo("ActivityState", "🎯 АКТИВНОСТЬ: intervalPaused=\(intervalPausedDueToInactivity), intervalReset=\(intervalResetDueToInactivity)")

        // НОВАЯ ЛОГИКА: Если интервал был приостановлен - возобновляем его
        if intervalPausedDueToInactivity && !intervalResetDueToInactivity {
            intervalPausedDueToInactivity = false
            onResumeCounters?()
            logInfo("ActivityState", "▶️ ИНТЕРВАЛ ВОЗОБНОВЛЕН после возвращения")
            startReturnStabilization()
        }

        // СТАРАЯ ЛОГИКА: Поддерживаем совместимость
        if countersStoppedDueToInactivity {
            startReturnStabilization()
        }

        // Сбрасываем флаги неактивности
        intervalPausedDueToInactivity = false
        intervalResetDueToInactivity = false
        countersStoppedDueToInactivity = false

        // Обновляем время последней активности и сбрасываем время неактивности
        lastActivityTime = Date()
        inactivityStartTime = nil

        // Останавливаем быстрое обнаружение возвращения
        stopQuickReturnDetection()

        // Переводим в состояние работы если не в формальном отдыхе
        if currentState != .formalRest {
            transitionToState(.working)
        }

        // Возобновляем счетчики если они были остановлены
        if countersStoppedDueToInactivity {
            countersStoppedDueToInactivity = false
            logInfo("ActivityState", "🎯 Возобновляем счетчики")
            onResumeCounters?()
        }

        // Проверяем стабилизацию возвращения
        checkReturnStabilization()
    }
    
    /// Обрабатывает неактивность пользователя
    private func handleUserInactivity() {
        let timeSinceLastActivity = Date().timeIntervalSince(lastActivityTime)

        // Сохраняем время начала неактивности если еще не сохранено
        if inactivityStartTime == nil {
            inactivityStartTime = Date()
        }

        // Сбрасываем стабилизацию возвращения при неактивности
        resetReturnStabilization()

        // НОВАЯ ЛОГИКА: Разумные пороги для интервалов

        // 1. ПРИОСТАНОВКА ИНТЕРВАЛА (10+ минут неактивности)
        if timeSinceLastActivity >= pauseIntervalThreshold && !intervalPausedDueToInactivity {
            // Проверяем есть ли активный интервал для приостановки
            if let timer = pomodoroTimer, timer.state == .working {
                intervalPausedDueToInactivity = true
                onStopCounters?()
                logInfo("ActivityState", "⏸️ ИНТЕРВАЛ ПРИОСТАНОВЛЕН: \(Int(timeSinceLastActivity/60)) мин неактивности")

                // Запускаем быстрое обнаружение возвращения
                startQuickReturnDetection()
            }
        }

        // 2. СБРОС ИНТЕРВАЛА (15+ минут неактивности)
        if timeSinceLastActivity >= resetIntervalThreshold && !intervalResetDueToInactivity {
            // Проверяем есть ли активный интервал для сброса
            if let timer = pomodoroTimer, (timer.state == .working || timer.state == .overtime) {
                intervalResetDueToInactivity = true

                // Сбрасываем интервал
                timer.stopInterval()
                logInfo("ActivityState", "🔄 ИНТЕРВАЛ СБРОШЕН: \(Int(timeSinceLastActivity/60)) мин неактивности")

                // Уведомляем о сбросе интервала
                onIntervalReset?()
            }
        }

        // 3. ЗАКРЫТИЕ ОКОН (10+ минут неактивности) - оставляем как было
        let longInactivityThreshold: TimeInterval = 10 * 60
        if timeSinceLastActivity >= longInactivityThreshold {
            let newState = determineAwayState(timeSinceLastActivity)
            if currentState != .awayLong && currentState != .awayVeryLong &&
               (newState == .awayLong || newState == .awayVeryLong) {
                logInfo("ActivityState", "🎯 Длительная неактивность (\(Int(timeSinceLastActivity/60)) мин) - закрываем окна")
                onLongInactivity?(timeSinceLastActivity)
            }
        }

        // Определяем состояние на основе времени отсутствия
        if currentState != .formalRest {
            let newState = determineAwayState(timeSinceLastActivity)
            transitionToState(newState)
        }
    }
    
    /// Определяет состояние отсутствия на основе времени
    private func determineAwayState(_ timeAway: TimeInterval) -> ActivityState {
        if timeAway < shortAwayThreshold {
            return .awayShort
        } else if timeAway < mediumAwayThreshold {
            return .awayMedium
        } else if timeAway < longAwayThreshold {
            return .awayLong
        } else {
            return .awayVeryLong
        }
    }
    
    /// Переводит в новое состояние
    private func transitionToState(_ newState: ActivityState) {
        guard newState != currentState else { return }
        
        let previousState = currentState
        currentState = newState
        stateStartTime = Date()
        
        logInfo("ActivityState", "🎯 Переход \(previousState) → \(newState)")
        onStateChanged?(previousState, newState)
    }
    
    /// Обрабатывает возвращение пользователя после отсутствия
    private func handleUserReturn() {
        let awayTime = Date().timeIntervalSince(lastActivityTime)

        // Проверяем выходные дни - если сегодня выходной, показываем выбор вместо СРВ
        if WeekendManager.shared.shouldShowWeekendChoice() {
            logInfo("ActivityState", "🏖️ Сегодня выходной - показываем выбор отдых/работа")
            onWeekendChoice?()
            return
        }

        let message = determineReturnMessage(awayTime)

        logInfo("ActivityState", "🎯 Пользователь вернулся после \(Int(awayTime/60)) мин, сообщение: \(message)")
        onUserReturned?(message, awayTime)
    }

    /// Начинает стабилизацию возвращения пользователя
    private func startReturnStabilization() {
        // Используем время начала неактивности для точного расчета
        let awayTime: TimeInterval
        if let inactivityStart = inactivityStartTime {
            awayTime = Date().timeIntervalSince(inactivityStart)
        } else {
            // Fallback на старый метод если время неактивности не сохранено
            awayTime = Date().timeIntervalSince(lastActivityTime)
        }

        // Сохраняем время отсутствия для последующего показа окна
        pendingReturnAwayTime = awayTime
        returnStabilizationStartTime = Date()

        logInfo("ActivityState", "🎯 Начинаем стабилизацию возвращения (отсутствие: \(Int(awayTime/60)) мин \(Int(awayTime.truncatingRemainder(dividingBy: 60))) сек)")

        // Запускаем таймер для проверки стабилизации каждые 0.5 секунды
        stabilizationTimer?.invalidate()
        stabilizationTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { [weak self] _ in
            self?.checkReturnStabilization()
        }
    }

    /// Проверяет стабилизацию возвращения и показывает окно если активность подтверждена
    private func checkReturnStabilization() {
        guard let awayTime = pendingReturnAwayTime,
              let stabilizationStart = returnStabilizationStartTime else {
            return
        }

        // Проверяем сколько времени прошло с начала стабилизации
        let stabilizationElapsed = Date().timeIntervalSince(stabilizationStart)

        // Проверяем текущую активность
        let mouseIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .mouseMoved)
        let keyboardIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .keyDown)
        let clickIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .leftMouseDown)
        let scrollIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .scrollWheel)

        let minIdleTime = min(mouseIdleTime, keyboardIdleTime, clickIdleTime, scrollIdleTime)
        let isCurrentlyActive = minIdleTime <= 3.0  // Активность в последние 3 секунды

        if !isCurrentlyActive {
            // Пользователь снова стал неактивен - сбрасываем стабилизацию
            logInfo("ActivityState", "🎯 Пользователь снова неактивен (последняя активность: \(String(format: "%.1f", minIdleTime))с) - сбрасываем стабилизацию")
            resetReturnStabilization()
            return
        }

        // Проверяем достаточно ли времени прошло для стабилизации
        if stabilizationElapsed >= returnStabilizationDuration {
            // Проверяем выходные дни - если сегодня выходной, показываем выбор вместо СРВ
            if WeekendManager.shared.shouldShowWeekendChoice() {
                logInfo("ActivityState", "🏖️ Стабилизация завершена, но сегодня выходной - показываем выбор отдых/работа")
                onWeekendChoice?()
            } else {
                let message = determineReturnMessage(awayTime)

                logInfo("ActivityState", "🎯 Стабилизация завершена (\(String(format: "%.1f", stabilizationElapsed))с активности) - показываем окно возвращения")

                onUserReturned?(message, awayTime)
            }

            // Сбрасываем стабилизацию и останавливаем таймер
            returnStabilizationStartTime = nil
            pendingReturnAwayTime = nil
            stabilizationTimer?.invalidate()
            stabilizationTimer = nil
        } else {
            let remaining = returnStabilizationDuration - stabilizationElapsed
            logInfo("ActivityState", "🎯 Ждем стабилизации: \(String(format: "%.1f", stabilizationElapsed))с/\(String(format: "%.1f", returnStabilizationDuration))с (осталось: \(String(format: "%.1f", remaining))с)")
        }
    }

    /// Сбрасывает стабилизацию возвращения при неактивности
    private func resetReturnStabilization() {
        if returnStabilizationStartTime != nil {
            logInfo("ActivityState", "🎯 Сброс стабилизации возвращения из-за неактивности")
            returnStabilizationStartTime = nil
            pendingReturnAwayTime = nil
            stabilizationTimer?.invalidate()
            stabilizationTimer = nil
        }
    }

    /// Запускает быстрое обнаружение возвращения пользователя
    private func startQuickReturnDetection() {
        // Останавливаем предыдущий таймер если есть
        quickReturnDetectionTimer?.invalidate()

        // Запускаем таймер проверки каждые 2 секунды
        quickReturnDetectionTimer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { [weak self] _ in
            self?.checkForQuickReturn()
        }

        logInfo("ActivityState", "🎯 Запущено быстрое обнаружение возвращения (каждые 2с)")
    }

    /// Останавливает быстрое обнаружение возвращения
    private func stopQuickReturnDetection() {
        quickReturnDetectionTimer?.invalidate()
        quickReturnDetectionTimer = nil
        logInfo("ActivityState", "🎯 Остановлено быстрое обнаружение возвращения")
    }

    /// Проверяет активность для быстрого обнаружения возвращения
    private func checkForQuickReturn() {
        // Проверяем только если пользователь неактивен и счетчики остановлены
        guard countersStoppedDueToInactivity else {
            return
        }

        // Проверяем текущую активность
        let mouseIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .mouseMoved)
        let keyboardIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .keyDown)
        let clickIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .leftMouseDown)
        let scrollIdleTime = CGEventSource.secondsSinceLastEventType(.hidSystemState, eventType: .scrollWheel)

        let minIdleTime = min(mouseIdleTime, keyboardIdleTime, clickIdleTime, scrollIdleTime)

        // Если обнаружена активность - имитируем возвращение
        if minIdleTime <= 3.0 {
            logInfo("ActivityState", "🎯 Быстрое обнаружение: активность найдена (последняя активность: \(String(format: "%.1f", minIdleTime))с)")

            // Останавливаем быстрое обнаружение
            stopQuickReturnDetection()

            // Имитируем сигнал от MinuteActivityTracker
            handleUserActivity()
        }
    }

    /// Определяет тип сообщения при возвращении
    private func determineReturnMessage(_ awayTime: TimeInterval) -> ReturnMessage {
        if awayTime < shortAwayThreshold {
            return .resumeSilently
        } else if awayTime < mediumAwayThreshold {
            return .partialRest
        } else if awayTime < longAwayThreshold {
            return .chooseRestOrWork
        } else {
            return .fullRest
        }
    }
    
    // MARK: - Debug Methods
    
    /// Возвращает детальную информацию для отладки
    func getDebugInfo() -> String {
        let timeInState = Date().timeIntervalSince(stateStartTime)
        let timeSinceActivity = Date().timeIntervalSince(lastActivityTime)

        var info = "🎯 НОВАЯ СИСТЕМА АКТИВНОСТИ (ActivityStateManager):\n"
        info += "================================\n"
        info += "📊 ОСНОВНАЯ ИНФОРМАЦИЯ:\n"
        info += "- Система активна: \(isActive ? "✅ ДА" : "❌ НЕТ")\n"
        info += "- Текущее состояние: \(getStateEmoji(currentState)) \(currentState)\n"
        info += "- Время в состоянии: \(formatTime(timeInState))\n"
        info += "- Время с последней активности: \(formatTime(timeSinceActivity))\n"
        info += "- Счетчики остановлены: \(countersStoppedDueToInactivity ? "⏸️ ДА" : "▶️ НЕТ")\n"
        info += "\n"

        // Информация о текущем трекере активности
        let (currentSegment, segments, isTrackerActive) = minuteTracker.getCurrentSegmentsState()
        info += "🔍 ТРЕКЕР АКТИВНОСТИ (20-ОТРЕЗКОВАЯ СИСТЕМА С ПОГРЕШНОСТЬЮ):\n"
        info += "- Трекер активен: \(isTrackerActive ? "✅ ДА" : "❌ НЕТ")\n"
        info += "- Текущий отрезок: \(currentSegment + 1)/20 (каждый отрезок = 3 сек)\n"
        info += "- Активных отрезков: \(segments.filter { $0 }.count)/20\n"
        info += "- Отрезки текущей минуты: "
        for (index, isActive) in segments.enumerated() {
            let symbol = isActive ? "🟢" : "⚫"
            info += "\(symbol)"
            if index < segments.count - 1 && (index + 1) % 5 == 0 { info += " " }
        }
        info += "\n"
        let activeSegments = segments.filter { $0 }.count
        info += "- Минута активна: \(activeSegments >= 2 ? "✅ ДА" : "❌ НЕТ") (нужно ≥2 отрезков, есть \(activeSegments))\n"
        info += "\n"

        // Дополнительная информация для понимания
        let timeToNext = minuteTracker.getTimeToNextSegmentCheck()
        info += "💡 ОБЪЯСНЕНИЕ:\n"
        info += "- Отрезки проверяются каждые 3 секунды\n"
        info += "- 🟢 = была активность в этом 3-сек интервале\n"
        info += "- ⚫ = не было активности в этом интервале\n"
        info += "- Логика погрешности: 1 отрезок = случайность, ≥2 отрезков = реальная работа\n"
        info += "- До следующей проверки: \(String(format: "%.1f", timeToNext)) сек\n"
        info += "- Если все ⚫ - подвигайте мышкой и подождите до следующего отрезка\n"

        return info
    }

    /// Возвращает эмодзи для состояния
    private func getStateEmoji(_ state: ActivityState) -> String {
        switch state {
        case .working: return "💻"
        case .awayShort: return "⏱️"
        case .awayMedium: return "⏰"
        case .awayLong: return "🕐"
        case .awayVeryLong: return "😴"
        case .formalRest: return "🍃"
        }
    }

    /// Форматирует время в читаемый вид
    private func formatTime(_ timeInterval: TimeInterval) -> String {
        let minutes = Int(timeInterval) / 60
        let seconds = Int(timeInterval) % 60

        if minutes > 0 {
            return "\(minutes) мин \(seconds) сек"
        } else {
            return "\(seconds) сек"
        }
    }
    
    /// Принудительно устанавливает состояние (для тестирования)
    func setForcedState(_ state: ActivityState) {
        let previousState = currentState
        currentState = state
        stateStartTime = Date()
        
        print("🧪 Принудительно установлено состояние \(state)")
        onStateChanged?(previousState, state)
    }
    
    /// Симулирует активность (для тестирования)
    func simulateActivity() {
        handleUserActivity()
    }
    
    /// Симулирует неактивность (для тестирования)
    func simulateInactivity(duration: TimeInterval) {
        lastActivityTime = Date().addingTimeInterval(-duration)
        handleUserInactivity()
    }

    /// Сбрасывает состояние менеджера активности
    func resetState() {
        currentState = .working
        stateStartTime = Date()
        lastActivityTime = Date()
        countersStoppedDueToInactivity = false

        // Сбрасываем историю неформальных сессий
        minuteActivityHistory.removeAll()
        lastRestSuggestionTime = nil

        print("🎯 ActivityStateManager: Состояние сброшено")
    }

    // MARK: - Informal Session Detection

    /// Записывает активность текущей минуты и проверяет неформальные сессии
    func recordMinuteActivity(isActive: Bool) {
        // Добавляем активность в историю
        minuteActivityHistory.append(isActive)

        // Ограничиваем размер истории
        if minuteActivityHistory.count > maxHistoryMinutes {
            minuteActivityHistory.removeFirst()
        }

        logInfo("ActivityState", "🔍 Записана активность минуты: \(isActive), история: \(minuteActivityHistory.count) мин")

        // Проверяем условия для предложения отдыха
        checkForInformalRestSuggestion()
    }

    /// Проверяет условия для показа предложения неформального отдыха
    private func checkForInformalRestSuggestion() {
        // КРИТИЧЕСКИ ВАЖНО: НЕ показываем неформальные предложения во время активного формального интервала ИЛИ отдыха
        if let timer = pomodoroTimer {
            if timer.state == .working {
                logInfo("ActivityState", "🔍 БЛОКИРОВКА: Активный формальный интервал (\(timer.state)) - НЕ показываем неформальное предложение")
                return
            } else if timer.state == .onBreak {
                logInfo("ActivityState", "🔍 БЛОКИРОВКА: Пользователь на отдыхе (\(timer.state)) - НЕ показываем неформальное предложение")
                return
            } else if timer.state == .overtime {
                logInfo("ActivityState", "🔍 БЛОКИРОВКА: Переработка (\(timer.state)) - НЕ показываем неформальное предложение")
                return
            }
        }

        // Нужно минимум 52 минуты истории
        guard minuteActivityHistory.count >= maxHistoryMinutes else {
            logInfo("ActivityState", "🔍 Недостаточно истории для проверки: \(minuteActivityHistory.count)/\(maxHistoryMinutes)")
            return
        }

        // Проверяем cooldown
        if let lastSuggestion = lastRestSuggestionTime,
           Date().timeIntervalSince(lastSuggestion) < restSuggestionCooldown {
            logInfo("ActivityState", "🔍 Cooldown активен, пропускаем проверку")
            return
        }

        // Считаем активные минуты
        let activeMinutes = minuteActivityHistory.filter { $0 }.count
        logInfo("ActivityState", "🔍 Активных минут: \(activeMinutes)/\(maxHistoryMinutes)")

        // Проверяем условие срабатывания
        if activeMinutes >= minActiveMinutesForSuggestion {
            logInfo("ActivityState", "🔔 НЕФОРМАЛЬНАЯ СЕССИЯ: \(activeMinutes) активных минут из \(maxHistoryMinutes)!")

            // Запоминаем время предложения
            lastRestSuggestionTime = Date()

            // Вызываем callback
            onInformalRestSuggestion?()
        }
    }

    deinit {
        stop()
    }
}

// MARK: - Extensions

extension ActivityStateManager.ActivityState: CustomStringConvertible {
    var description: String {
        switch self {
        case .working: return "Работа"
        case .awayShort: return "Отошел ненадолго"
        case .awayMedium: return "Отошел на среднее время"
        case .awayLong: return "Отошел надолго"
        case .awayVeryLong: return "Отошел очень надолго"
        case .formalRest: return "Формальный отдых"
        }
    }
}

extension ActivityStateManager.ReturnMessage: CustomStringConvertible {
    var description: String {
        switch self {
        case .resumeSilently: return "Продолжить молча"
        case .partialRest: return "Частичный отдых"
        case .chooseRestOrWork: return "Выбор отдых/работа"
        case .fullRest: return "Полноценный отдых"
        }
    }
}
